<!DOCTYPE html>
<html lang="en-us" dir="ltr">
<head>
	<meta name="generator" content="Hugo 0.145.0"><script src="/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=livereload" data-no-instant defer></script>
  <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Zeus Network | Bitcoin to Solana Made Seamless</title>
<meta name="description" content="Multi-chain Layer on Solana: Zeus Network enables interaction across leading blockchain ecosystems">
<meta name="theme-color" content="#0a0a0a">


<meta property="og:type" content="website">
<meta property="og:url" content="http://localhost:1313/">
<meta property="og:title" content="Zeus Network | Bitcoin to Solana Made Seamless">
<meta property="og:description" content="Multi-chain Layer on Solana: Zeus Network enables interaction across leading blockchain ecosystems">


<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:url" content="http://localhost:1313/">
<meta property="twitter:title" content="Zeus Network | Bitcoin to Solana Made Seamless">
<meta property="twitter:description" content="Multi-chain Layer on Solana: Zeus Network enables interaction across leading blockchain ecosystems">


    <link rel="stylesheet" href="/css/main.css">


      <script src="/js/main.js"></script>


</head>
<body>
  <header>
    <div class="container">
  <div class="header-content">
    <div class="logo">
      <a href="/">Zeus Network</a>
    </div>
    <nav>
      <ul class="nav-menu">
        
          <li><a href="#ecosystem">Ecosystem</a></li>
        
          <li><a href="#developers">Developers</a></li>
        
          <li><a href="/zeus-stack">Start Building</a></li>
        
      </ul>
    </nav>
  </div>
</div>

  </header>
  <main>
    

<section class="hero">
  <div class="bg-effect"></div>
  <div class="container">
    <div class="hero-content fade-in-up">
      <h1>Cross-chain Made Seamless</h1>
      <p>Multi-chain Layer on Solana: Zeus Network enables interaction across leading blockchain ecosystems</p>
      <div class="hero-buttons">
        <a href="/zeus-stack" class="btn btn-primary">Build with ZeusStack</a>
        <a href="https://app.zeusguardian.io/" class="btn btn-secondary">Delegate $ZEUS</a>
      </div>
    </div>
  </div>
</section>


<section id="ecosystem" class="section-dark">
  <div class="container">
    <h2 style="text-align: center; margin-bottom: 3rem;">Ecosystem</h2>
    <h3 style="text-align: center; margin-bottom: 4rem; color: var(--secondary-text);">dApps by ZeusStack</h3>

    <div class="grid grid-3">
      <div class="ecosystem-card">
        <div class="ecosystem-logo">A</div>
        <h3>Apollo</h3>
        <p>Maximize on-chain yield on Solana with native BTC — the Ultimate Bitcoin On-Chain Exchange.</p>
        <div class="ecosystem-buttons">
          <a href="https://apollodex.io/" class="btn btn-primary">Explore</a>
          <a href="https://docs.apollodex.io/" class="btn btn-secondary">Docs</a>
        </div>
      </div>

      <div class="ecosystem-card">
        <div class="ecosystem-logo">B</div>
        <h3>BTC Sol</h3>
        <p>A new yield primitive on Solana that helps SOL holders supercharge their staking rewards powered by BTC.</p>
        <div class="ecosystem-buttons">
          <a href="https://btcsol.co/" class="btn btn-primary">Explore</a>
          <a href="https://docs.btcsol.co/" class="btn btn-secondary">Docs</a>
        </div>
      </div>

      <div class="ecosystem-card">
        <div class="ecosystem-logo">L</div>
        <h3>LightningFi</h3>
        <p>Native BTC in, yield out — all without leaving Bitcoin. One-Stop Yield Aggregator for Native BTC on Solana.</p>
        <div class="ecosystem-buttons">
          <a href="https://lightningfi.xyz/" class="btn btn-primary">Explore</a>
          <a href="https://docs.lightningfi.xyz/" class="btn btn-secondary">Docs</a>
        </div>
      </div>
    </div>
  </div>
</section>


<section>
  <div class="container">
    <h2 style="text-align: center; margin-bottom: 1rem;">Angel Investors</h2>
    <h3 style="text-align: center; margin-bottom: 4rem; color: var(--secondary-text);">Meet Our Backers</h3>

    <div class="grid grid-3">
      <div class="investor-card">
        <div class="investor-avatar">AY</div>
        <div class="investor-name">Anatoly Yakovenko</div>
        <div class="investor-title">Co-Founder, Solana</div>
      </div>

      <div class="investor-card">
        <div class="investor-avatar">MA</div>
        <div class="investor-name">Muneeb Ali</div>
        <div class="investor-title">Co-Founder, Stacks</div>
      </div>

      <div class="investor-card">
        <div class="investor-avatar">AK</div>
        <div class="investor-name">Andrew Kang</div>
        <div class="investor-title">Founder, Mechanism Capital</div>
      </div>
    </div>
  </div>
</section>


<section class="section-dark">
  <div class="container">
    <h2 style="text-align: center; margin-bottom: 4rem;">Backers</h2>

    <div class="backers-grid">
      <div class="backer-logo">Mechanism Capital</div>
      <div class="backer-logo">OKX Ventures</div>
      <div class="backer-logo">Anagram</div>
      <div class="backer-logo">Animoca Ventures</div>
      <div class="backer-logo">UTXO</div>
      <div class="backer-logo">Appworks</div>
      <div class="backer-logo">Axia8 Ventures</div>
      <div class="backer-logo">Comma3 Ventures</div>
      <div class="backer-logo">IVC</div>
      <div class="backer-logo">Spartan</div>
      <div class="backer-logo">Karatage</div>
      <div class="backer-logo">Lemniscap</div>
      <div class="backer-logo">Portal Ventures</div>
    </div>
  </div>
</section>


<section>
  <div class="container">
    <h2 style="text-align: center; margin-bottom: 4rem;">Empowering Seamless Cross-chain Interactions</h2>

    <div class="mission-visual">
      <div class="chain-icon bitcoin">₿</div>
      <div class="chain-connector"></div>
      <div class="chain-icon solana">◎</div>
    </div>
  </div>
</section>


<section class="section-dark">
  <div class="container">
    <h2 style="text-align: center; margin-bottom: 1rem;">Mission</h2>
    <h3 style="text-align: center; margin-bottom: 4rem; color: var(--secondary-text);">Building a Multi-chain Layer on Solana</h3>

    <div class="grid grid-2">
      <div class="card">
        <h3>Cross-chain Liquidity</h3>
        <p>Enabling seamless interoperability of liquidity and assets into Solana ecosystem.</p>
      </div>

      <div class="card">
        <h3>Multi-chain Ecosystem</h3>
        <p>Building a robust ecosystem across leading blockchains for DeFi and asset applications.</p>
      </div>

      <div class="card">
        <h3>Network Security</h3>
        <p>Securing ZeusNode operations through advanced cryptography verification and an MPC validation model built on SVM.</p>
      </div>

      <div class="card">
        <h3>Open-Sourcing ZPL</h3>
        <p>The Zeus Program Library (ZPL) framework will be opened for developers to create multi-chain dApps.</p>
      </div>
    </div>
  </div>
</section>


<section>
  <div class="container">
    <h2 style="text-align: center; margin-bottom: 1rem;">Security Audits</h2>
    <h3 style="text-align: center; margin-bottom: 4rem; color: var(--secondary-text);">Proven secure by leading auditors</h3>

    <div class="grid grid-3">
      <div class="card">
        <h3>Sec3</h3>
        <p style="color: #10b981;">✓ Completed</p>
      </div>

      <div class="card">
        <h3>Sec3</h3>
        <p style="color: #f59e0b;">⏳ In Progress</p>
      </div>

      <div class="card">
        <h3>Sec3</h3>
        <p style="color: var(--secondary-text);">🔜 Coming Soon</p>
      </div>
    </div>
  </div>
</section>


<section class="section-dark">
  <div class="container">
    <h2 style="text-align: center; margin-bottom: 4rem;">Ecosystem Tools</h2>

    <div class="grid grid-2">
      <div class="card">
        <h3>ZeusScan</h3>
        <p>Monitor BTC on-chain proof of reserves, explore Bitcoin-Solana interactions, and track network stats.</p>
        <a href="https://zeusscan.io/" class="btn btn-primary">Explore</a>
      </div>

      <div class="card">
        <h3>Zeus Guardian</h3>
        <p>Delegate ZEUS tokens with Guardian to increase BTC capacity, secure ZPL-assets, and earn network rewards.</p>
        <a href="https://app.zeusguardian.io/" class="btn btn-primary">Delegate</a>
      </div>
    </div>
  </div>
</section>


  </main>
  <footer>
    <div class="container">
  <div class="footer-content">
    <div class="footer-section">
      <div class="logo" style="margin-bottom: 1rem;">Zeus Network</div>
      <p>Multi-chain Layer on Solana enabling seamless cross-chain interactions.</p>
    </div>

    <div class="footer-section">
      <h4>dApps</h4>
      <ul>
        <li><a href="https://apollodex.io/">APOLLO</a></li>
        <li><a href="https://btcsol.co/">btcSOL</a></li>
        <li><a href="https://lightningfi.xyz/">LightningFi</a></li>
      </ul>
    </div>

    <div class="footer-section">
      <h4>Developers</h4>
      <ul>
        <li><a href="/zeus-stack">ZeusStack</a></li>
        <li><a href="https://github.com/ZeusNetworkHQ">GitHub</a></li>
      </ul>
    </div>

    <div class="footer-section">
      <h4>Community</h4>
      <ul>
        <li><a href="https://x.com/zeusnetworkhq">X (Twitter)</a></li>
        <li><a href="https://discord.gg/zeusnetwork">Discord</a></li>
        <li><a href="https://medium.com/@zeus-network">Medium</a></li>
      </ul>
    </div>
  </div>

  <div class="footer-bottom">
    <p>&copy; 2025, All rights reserved.</p>
  </div>
</div>

  </footer>
</body>
</html>

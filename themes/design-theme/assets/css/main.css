/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-bg: #0a0a0a;
  --secondary-bg: #1a1a1a;
  --accent-bg: #2a2a2a;
  --primary-text: #ffffff;
  --secondary-text: #b0b0b0;
  --accent-text: #8b5cf6;
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --border-color: #333;
  --hover-bg: #333;
}

body {
  background: var(--primary-bg);
  color: var(--primary-text);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
  margin: 0;
  overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2rem);
}

p {
  margin-bottom: 1rem;
  color: var(--secondary-text);
}

/* Links */
a {
  color: var(--accent-text);
  text-decoration: none;
  transition: all 0.3s ease;
}

a:hover {
  color: var(--primary-text);
  transform: translateY(-2px);
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  text-decoration: none;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
}

.btn-secondary {
  background: transparent;
  color: var(--primary-text);
  border: 2px solid var(--border-color);
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Grid system */
.grid {
  display: grid;
  gap: 2rem;
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Sections */
section {
  padding: 80px 0;
  position: relative;
}

.section-dark {
  background: var(--secondary-bg);
}

/* Header */
header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(10, 10, 10, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: 1000;
  padding: 1rem 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
  align-items: center;
}

.nav-menu a {
  color: var(--secondary-text);
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-menu a:hover {
  color: var(--primary-text);
}

/* Hero section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: radial-gradient(ellipse at center, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
  position: relative;
  overflow: hidden;
}

.hero-content {
  max-width: 800px;
  z-index: 2;
}

.hero h1 {
  margin-bottom: 1.5rem;
}

.hero p {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  color: var(--secondary-text);
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Background effects */
.bg-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.bg-effect::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  animation: rotate 20s linear infinite;
}

/* Animations */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

/* Cards */
.card {
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  border-color: var(--accent-text);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.card:hover::before {
  transform: scaleX(1);
}

.card h3 {
  color: var(--primary-text);
  margin-bottom: 1rem;
}

.card p {
  color: var(--secondary-text);
  margin-bottom: 1.5rem;
}

/* Ecosystem section */
.ecosystem-card {
  background: linear-gradient(135deg, var(--secondary-bg) 0%, var(--accent-bg) 100%);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 2.5rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.ecosystem-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.ecosystem-card:hover::before {
  opacity: 0.1;
}

.ecosystem-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

.ecosystem-logo {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  color: white;
}

.ecosystem-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 1.5rem;
}

.ecosystem-buttons .btn {
  padding: 8px 16px;
  font-size: 0.9rem;
}

/* Investor cards */
.investor-card {
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.investor-card:hover {
  transform: translateY(-5px);
  border-color: var(--accent-text);
}

.investor-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin: 0 auto 1rem;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  color: white;
}

.investor-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.investor-title {
  color: var(--secondary-text);
  font-size: 0.9rem;
}

/* Backers section */
.backers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.backer-logo {
  height: 60px;
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  color: var(--secondary-text);
  font-weight: 600;
}

.backer-logo:hover {
  border-color: var(--accent-text);
  transform: translateY(-2px);
}

/* Mission section */
.mission-visual {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3rem;
  margin: 3rem 0;
  flex-wrap: wrap;
}

.chain-icon {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  font-weight: bold;
  color: white;
  position: relative;
}

.chain-icon.bitcoin {
  background: linear-gradient(135deg, #f7931a 0%, #ff6b35 100%);
}

.chain-icon.solana {
  background: linear-gradient(135deg, #9945ff 0%, #14f195 100%);
}

.chain-connector {
  width: 100px;
  height: 4px;
  background: var(--gradient-primary);
  position: relative;
  border-radius: 2px;
}

.chain-connector::before {
  content: '⚡';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--primary-bg);
  padding: 0.5rem;
  border-radius: 50%;
  font-size: 1.5rem;
}

/* Footer */
footer {
  background: var(--secondary-bg);
  border-top: 1px solid var(--border-color);
  padding: 3rem 0 2rem;
  margin-top: 4rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h4 {
  margin-bottom: 1rem;
  color: var(--primary-text);
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
}

.footer-section ul li a {
  color: var(--secondary-text);
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: var(--primary-text);
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
  color: var(--secondary-text);
}

/* Responsive design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-menu {
    gap: 1rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .mission-visual {
    flex-direction: column;
    gap: 2rem;
  }

  .chain-connector {
    width: 4px;
    height: 50px;
    transform: rotate(90deg);
  }
}

<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>{{ if .IsHome }}{{ site.Title }}{{ else }}{{ printf "%s | %s" .Title site.Title }}{{ end }}</title>
<meta name="description" content="{{ if .IsHome }}{{ site.Params.description }}{{ else }}{{ .Summary | default site.Params.description }}{{ end }}">
<meta name="theme-color" content="#0a0a0a">

<!-- Open Graph / Facebook -->
<meta property="og:type" content="website">
<meta property="og:url" content="{{ .Permalink }}">
<meta property="og:title" content="{{ if .IsHome }}{{ site.Title }}{{ else }}{{ printf "%s | %s" .Title site.Title }}{{ end }}">
<meta property="og:description" content="{{ if .IsHome }}{{ site.Params.description }}{{ else }}{{ .Summary | default site.Params.description }}{{ end }}">

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:url" content="{{ .Permalink }}">
<meta property="twitter:title" content="{{ if .IsHome }}{{ site.Title }}{{ else }}{{ printf "%s | %s" .Title site.Title }}{{ end }}">
<meta property="twitter:description" content="{{ if .IsHome }}{{ site.Params.description }}{{ else }}{{ .Summary | default site.Params.description }}{{ end }}">

{{ partialCached "head/css.html" . }}
{{ partialCached "head/js.html" . }}
